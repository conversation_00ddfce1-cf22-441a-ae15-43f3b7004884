name: Tests Visuals

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./app

    steps:
      - uses: actions/checkout@v4
      
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          
      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Install Playwright browsers
        run: npx playwright install --with-deps  

      - name: Install Playwright
        run: npm i -D @playwright/test --legacy-peer-deps

      - name: Run Playwright tests
        run: npm run test:visuals 
        env:
          VITE_ENV: prod
          VITE_BASE_URL: https://tambia1.github.io/v
              
      - name: Upload Playwright Report
        if: ${{ !cancelled() }}
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: ./app/visuals-temp/report  
          retention-days: 5
