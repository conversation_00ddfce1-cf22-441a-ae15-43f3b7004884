name: Deploy to Pages

on:
    push:
        tags:
            - 'Live'

    workflow_dispatch:

permissions:
    contents: read
    pages: write
    id-token: write

concurrency:
    group: 'pages'
    cancel-in-progress: false

jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Setup Pages
              uses: actions/configure-pages@v5

            - name: Upload artifact
              uses: actions/upload-pages-artifact@v3
              with:
                  path: './app/dist'

            - name: Deploy to GitHub Pages
              id: deployment
              uses: actions/deploy-pages@v4
        env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
            NODE_ENV: production
