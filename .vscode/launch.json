{"version": "0.2.0", "compounds": [{"name": "Run project", "configurations": ["launch brave 5000", "app start", "server websocket", "server redis"], "stopAll": true, "presentation": {"group": "1", "order": 1}}], "configurations": [{"name": "launch edge 5000", "type": "msedge", "request": "launch", "url": "https://localhost:5000/v", "webRoot": "${workspaceFolder}/app", "sourceMapPathOverrides": {"\\mnt\\c\\*": "C:/*"}}, {"name": "launch brave 5000", "type": "chrome", "request": "launch", "url": "https://localhost:5000/v", "webRoot": "${workspaceFolder}/app", "runtimeExecutable": "/usr/bin/brave-browser-stable", "runtimeArgs": ["--new-window"], "sourceMapPathOverrides": {"\\mnt\\c\\*": "C:/*"}, "sourceMaps": true}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}, {"name": "app updates", "command": "npx npm-check-updates -u", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app install", "command": "npm install --legacy-peer-deps", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app start", "command": "npm run start", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/", "env": {"ENV": "dev"}}, {"name": "app ts check", "command": "npx tsc --noEmit", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app test units", "command": "npm run test:units", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app test visuals", "command": "npm run test:visuals", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/", "env": {"ENV": "prod"}}, {"name": "app coverage", "command": "npm run coverage", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app build", "command": "npm run build", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app preview", "command": "npm run preview", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app deploy", "command": "npm run deploy", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "app release", "command": "npm run release", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/src/"}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}, {"name": "env dev", "command": "export ENV='dev'", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "env qa", "command": "export ENV='qa'", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "env prod", "command": "export ENV='prod'", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "env list", "command": "printenv", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}, {"name": "playwright install", "command": "npx playwright install chromium", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "playwright test visuals", "command": "npm run test:visuals", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/", "env": {"ENV": "prod"}}, {"name": "playwright test visuals - show", "command": "npm run test:visuals:show", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/", "env": {"ENV": "prod"}}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}, {"name": "app create v application", "command": "node createApp.js", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/scripts/"}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}, {"name": "server websocket", "command": "sudo tsx WebSocketServer.ts", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/src/pages/pageHome/components/desktop/apps/chat/server/"}, {"name": "server redis", "command": "node index.js", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/src/pages/pageHome/components/desktop/apps/testRedis/server/"}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}, {"name": "Show versions", "command": "npm -v && node -v && tsc -v ", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "Update npm", "command": "npm install -g npm stable", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "Update node", "command": "npm install -g node stable", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "Update typescript", "command": "npm install -g typescript", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "Update vscode", "command": "sudo apt update && sudo apt install code", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}, {"name": "ip status", "command": "ip addr show", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "ipv4 status", "command": "ip -4 addr show", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "ipv6 status", "command": "ip -6 addr show | grep 'scope global temporary dynamic' | awk '{print $2}' | cut -d/ -f1 | tail -n 1", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "create ssl keys", "command": "openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "port status", "command": "sudo ufw status", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "port enable", "command": "sudo ufw allow 5002/tcp && sudo ufw reload", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "port disable", "command": "sudo ufw delete allow 5002/tcp && sudo ufw reload", "request": "launch", "type": "node-terminal", "cwd": "${workspaceFolder}/app/"}, {"name": "---------------------------------", "command": "", "request": "launch", "type": "node-terminal"}]}